.sidebar {
  width: 250px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background-color: var(--color-primary);
  color: var(--color-white);
  transition: var(--transition-default);
  z-index: var(--z-index-fixed);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebarCollapsed {
  width: 70px;
}

.logo {
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logoImage {
  height: 40px;
  transition: var(--transition-default);
}

.logoText {
  margin-left: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-white);
  transition: var(--transition-default);
  white-space: nowrap;
}

.sidebarCollapsed .logoText {
  display: none;
}

.navItems {
  padding: var(--spacing-md) 0;
  flex: 1;
}

.navItem {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--color-white);
  text-decoration: none;
  transition: var(--transition-default);
  border-left: 3px solid transparent;
}

.navItem:hover {
  background-color: var(--color-secondary);
  border-left-color: var(--color-accent);
}

.navItemActive {
  background-color: var(--color-secondary);
  border-left-color: var(--color-accent);
}

.navIcon {
  font-size: var(--font-size-xl);
  min-width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navText {
  margin-left: var(--spacing-md);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  transition: var(--transition-default);
}

.sidebarCollapsed .navText {
  display: none;
}

.sectionTitle {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  color: var(--color-medium-gray);
  letter-spacing: 1px;
  margin-top: var(--spacing-md);
}

.sidebarCollapsed .sectionTitle {
  display: none;
}

.userSection {
  padding: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
}

.userAvatar {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-full);
  background-color: var(--color-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  color: var(--color-white);
}

.userInfo {
  margin-left: var(--spacing-md);
  transition: var(--transition-default);
}

.sidebarCollapsed .userInfo {
  display: none;
}

.userName {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.userRole {
  font-size: var(--font-size-xs);
  color: var(--color-medium-gray);
}

/* Tooltip for collapsed sidebar */
.tooltip {
  position: relative;
}

.sidebarCollapsed .tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--color-secondary);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  z-index: var(--z-index-tooltip);
  margin-left: var(--spacing-sm);
}

/* Mobile sidebar */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 250px;
  }
  
  .sidebarOpen {
    transform: translateX(0);
  }
  
  .sidebarCollapsed {
    transform: translateX(-100%);
  }
  
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--z-index-fixed) - 1);
    display: none;
  }
  
  .overlayVisible {
    display: block;
  }
}
