@import './styles/variables.css';

@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
    font-family: var(--font-family-sans);
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-300;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors duration-300;
  }

  .card {
    @apply bg-white rounded-lg shadow p-6;
  }

  .input-field {
    @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500;
  }

  .label {
    @apply block text-sm font-medium text-gray-700;
  }
}
