.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.leftSection {
  display: flex;
  align-items: center;
}

.menuToggle {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--spacing-xs);
  margin-right: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
  transition: var(--transition-fast);
}

.menuToggle:hover {
  background-color: var(--color-light-gray);
}

.pageTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
}

.rightSection {
  display: flex;
  align-items: center;
}

.searchBar {
  position: relative;
  margin-right: var(--spacing-lg);
}

.searchInput {
  padding: var(--spacing-sm) var(--spacing-md);
  padding-left: calc(var(--spacing-md) + 24px); /* Space for icon */
  border: 1px solid var(--color-medium-gray);
  border-radius: var(--border-radius-full);
  background-color: var(--color-light-gray);
  width: 250px;
  transition: var(--transition-fast);
}

.searchInput:focus {
  width: 300px;
  background-color: var(--color-white);
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(62, 111, 191, 0.2);
}

.searchIcon {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-dark-gray);
  font-size: var(--font-size-lg);
}

.iconButton {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-xs);
  margin-left: var(--spacing-md);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-full);
  transition: var(--transition-fast);
}

.iconButton:hover {
  background-color: var(--color-light-gray);
}

.notificationBadge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-error);
  color: var(--color-white);
  font-size: var(--font-size-xs);
  width: 18px;
  height: 18px;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
}

.userMenu {
  display: flex;
  align-items: center;
  margin-left: var(--spacing-lg);
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  transition: var(--transition-fast);
}

.userMenu:hover {
  background-color: var(--color-light-gray);
}

.userAvatar {
  width: 36px;
  height: 36px;
  border-radius: var(--border-radius-full);
  background-color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  color: var(--color-white);
}

.userInfo {
  margin-left: var(--spacing-sm);
}

.userName {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--color-primary);
}

.userRole {
  font-size: var(--font-size-xs);
  color: var(--color-dark-gray);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .searchBar {
    display: none;
  }
  
  .pageTitle {
    font-size: var(--font-size-base);
  }
  
  .userInfo {
    display: none;
  }
}
