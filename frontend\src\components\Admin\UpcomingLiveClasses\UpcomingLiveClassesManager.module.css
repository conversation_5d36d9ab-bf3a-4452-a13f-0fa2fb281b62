.container {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-light-gray);
}

.title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.titleIcon {
  color: var(--color-accent);
}

.addButton {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-default);
}

.addButton:hover {
  background-color: var(--color-secondary);
}

.tableContainer {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background-color: var(--color-light-gray);
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  text-align: left;
  padding: var(--spacing-sm) var(--spacing-md);
}

.table td {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--color-light-gray);
}

.table tr:last-child td {
  border-bottom: none;
}

.table tr:hover {
  background-color: rgba(17, 37, 75, 0.05);
}

.statusBadge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.statusScheduled {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--color-info);
}

.statusOngoing {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
}

.statusCompleted {
  background-color: rgba(107, 114, 128, 0.1);
  color: var(--color-dark-gray);
}

.statusCancelled {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--color-error);
}

.statusUpcoming {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--color-warning);
}

.actions {
  display: flex;
  gap: var(--spacing-sm);
}

.actionButton {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-full);
  transition: var(--transition-default);
}

.editButton {
  color: var(--color-info);
  background-color: rgba(59, 130, 246, 0.1);
}

.editButton:hover {
  background-color: rgba(59, 130, 246, 0.2);
}

.deleteButton {
  color: var(--color-error);
  background-color: rgba(239, 68, 68, 0.1);
}

.deleteButton:hover {
  background-color: rgba(239, 68, 68, 0.2);
}

.viewButton {
  color: var(--color-primary);
  background-color: rgba(17, 37, 75, 0.1);
}

.viewButton:hover {
  background-color: rgba(17, 37, 75, 0.2);
}

.linkButton {
  color: var(--color-success);
  background-color: rgba(16, 185, 129, 0.1);
}

.linkButton:hover {
  background-color: rgba(16, 185, 129, 0.2);
}

.emptyState {
  text-align: center;
  padding: var(--spacing-xl) 0;
}

.emptyStateIcon {
  font-size: 3rem;
  color: var(--color-medium-gray);
  margin-bottom: var(--spacing-md);
}

.emptyStateText {
  color: var(--color-dark-gray);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
}

.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: var(--spacing-lg);
  gap: var(--spacing-sm);
}

.paginationButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-medium-gray);
  background-color: var(--color-white);
  color: var(--color-primary);
  cursor: pointer;
  transition: var(--transition-default);
}

.paginationButton:hover {
  background-color: var(--color-light-gray);
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.paginationActive {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}

.paginationActive:hover {
  background-color: var(--color-secondary);
}

.searchContainer {
  margin-bottom: var(--spacing-lg);
}

.searchInput {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-left: calc(var(--spacing-md) + 24px);
  border: 1px solid var(--color-medium-gray);
  border-radius: var(--border-radius-md);
  background-color: var(--color-white);
  position: relative;
}

.searchIcon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-dark-gray);
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.modalContent {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-light-gray);
}

.modalTitle {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-xl);
  color: var(--color-dark-gray);
}

.formGroup {
  margin-bottom: var(--spacing-md);
}

.formRow {
  display: flex;
  gap: var(--spacing-md);
}

.formColumn {
  flex: 1;
}

.formLabel {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

.formInput, .formSelect, .formTextarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-medium-gray);
  border-radius: var(--border-radius-md);
  background-color: var(--color-white);
}

.formInput:focus, .formSelect:focus, .formTextarea:focus {
  border-color: var(--color-accent);
  outline: none;
  box-shadow: 0 0 0 2px rgba(62, 111, 191, 0.2);
}

.formTextarea {
  min-height: 100px;
  resize: vertical;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.cancelButton {
  background-color: var(--color-light-gray);
  color: var(--color-dark-gray);
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-default);
}

.cancelButton:hover {
  background-color: var(--color-medium-gray);
}

.saveButton {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-default);
}

.saveButton:hover {
  background-color: var(--color-secondary);
}

.saveButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.errorText {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .formRow {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .table th:nth-child(3),
  .table td:nth-child(3) {
    display: none;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .cancelButton, .saveButton {
    width: 100%;
  }
}
